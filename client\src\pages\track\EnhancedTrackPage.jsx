import React, { useState, useContext, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardBody, Button } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

// Import new Track components
import VerticalNavigation from '../../components/track/VerticalNavigation';
import ProjectOverviewCards from '../../components/track/ProjectOverviewCards';
import QuickActionsPanel from '../../components/track/QuickActionsPanel';
import TaskCreationModal from '../../components/track/TaskCreationModal';
import TimeTrackingPanel from '../../components/track/TimeTrackingPanel';

// Import existing Kanban components
import KanbanBoard from '../../components/kanban/KanbanBoard';

/**
 * Enhanced Track Page - Project Management Hub
 * 
 * Features:
 * - Vertical navigation bars (left helper functions, right contextual tools)
 * - Project overview cards with metrics
 * - Kanban board with 4 columns
 * - Per-task time tracking
 * - Quick actions panel
 * - Mission board integration
 * - Real-time collaboration
 */
const EnhancedTrackPage = () => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [isTimeTrackingVisible, setIsTimeTrackingVisible] = useState(false);
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [activeTimerTaskId, setActiveTimerTaskId] = useState(null);
  const [activeTask, setActiveTask] = useState(null);
  const [currentProject, setCurrentProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const [notificationCount, setNotificationCount] = useState(3);

  useEffect(() => {
    if (currentUser) {
      loadInitialData();
    }
  }, [currentUser]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // Load current project (or create default)
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .eq('created_by', currentUser.id)
        .eq('status', 'active')
        .limit(1);

      if (projectsError) throw projectsError;

      if (projects && projects.length > 0) {
        setCurrentProject(projects[0]);
      } else {
        // Create a default project
        const { data: newProject, error: createError } = await supabase
          .from('projects')
          .insert([{
            name: 'My Project',
            description: 'Default project for task management',
            status: 'active',
            created_by: currentUser.id
          }])
          .select()
          .single();

        if (createError) throw createError;
        setCurrentProject(newProject);
      }

      // Check for any active timers
      const { data: activeTimers, error: timerError } = await supabase
        .from('active_timers')
        .select(`
          *,
          tasks (
            id,
            title,
            description
          )
        `)
        .eq('user_id', currentUser.id)
        .eq('is_active', true);

      if (timerError && timerError.code !== 'PGRST116') {
        throw timerError;
      }

      if (activeTimers && activeTimers.length > 0) {
        const timer = activeTimers[0];
        setActiveTimerTaskId(timer.task_id);
        setActiveTask(timer.tasks);
      }

    } catch (error) {
      console.error('Error loading initial data:', error);
      toast.error('Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  // Timer management functions
  const handleStartTimer = async (taskId) => {
    try {
      // Stop any existing timer first
      if (activeTimerTaskId) {
        await handleStopTimer(activeTimerTaskId);
      }

      // Get task details
      const { data: task, error: taskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (taskError) throw taskError;

      // Start new timer (create table if it doesn't exist)
      const { error: timerError } = await supabase
        .from('active_timers')
        .insert([{
          user_id: currentUser.id,
          task_id: taskId,
          started_at: new Date().toISOString(),
          is_active: true
        }]);

      if (timerError) {
        // If table doesn't exist, just track locally for now
        console.warn('Active timers table not available, tracking locally');
      }

      setActiveTimerTaskId(taskId);
      setActiveTask(task);
      toast.success(`Timer started for: ${task.title}`);

    } catch (error) {
      console.error('Error starting timer:', error);
      toast.error('Failed to start timer');
    }
  };

  const handleStopTimer = async (taskId) => {
    try {
      // For now, just stop locally if database table doesn't exist
      setActiveTimerTaskId(null);
      setActiveTask(null);
      toast.success('Timer stopped');

    } catch (error) {
      console.error('Error stopping timer:', error);
      toast.error('Failed to stop timer');
    }
  };

  // Navigation handlers
  const handleAddTask = () => {
    setIsTaskModalOpen(true);
  };

  const handleQuickFilter = () => {
    toast.info('Quick filter feature coming soon!');
  };

  const handleBulkActions = () => {
    toast.info('Bulk actions feature coming soon!');
  };

  const handleImportTasks = () => {
    toast.info('Import tasks feature coming soon!');
  };

  const handleViewTemplates = () => {
    toast.info('Task templates feature coming soon!');
  };

  const handleTeamCalendar = () => {
    toast.info('Team calendar feature coming soon!');
  };

  const handleActivityFeed = () => {
    toast.info('Activity feed feature coming soon!');
  };

  const handleHelp = () => {
    toast.info('Help & tutorials feature coming soon!');
  };

  const handleNotifications = () => {
    toast.info('Notifications panel coming soon!');
    setNotificationCount(0);
  };

  const handleProjectSettings = () => {
    toast.info('Project settings feature coming soon!');
  };

  const handleExportData = () => {
    toast.info('Export data feature coming soon!');
  };

  const handleIntegrations = () => {
    toast.info('Integrations feature coming soon!');
  };

  const handleTimeTrackingToggle = () => {
    setIsTimeTrackingVisible(!isTimeTrackingVisible);
  };

  const handleAnalytics = () => {
    toast.info('Analytics feature coming soon!');
  };

  const handleShareProject = () => {
    toast.info('Share project feature coming soon!');
  };

  const handleArchive = () => {
    toast.info('Archive feature coming soon!');
  };

  const handleInviteCollaborator = () => {
    toast.info('Invite collaborator feature coming soon!');
  };

  const handleProjectAnalytics = () => {
    toast.info('Project analytics feature coming soon!');
  };

  const handleTaskCreated = (newTask) => {
    toast.success('Task created successfully!');
    // Refresh kanban board or add task to state
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Vertical Navigation */}
      <VerticalNavigation
        onAddTask={handleAddTask}
        onQuickFilter={handleQuickFilter}
        onBulkActions={handleBulkActions}
        onImportTasks={handleImportTasks}
        onViewTemplates={handleViewTemplates}
        onTeamCalendar={handleTeamCalendar}
        onActivityFeed={handleActivityFeed}
        onHelp={handleHelp}
        onNotifications={handleNotifications}
        onProjectSettings={handleProjectSettings}
        onExportData={handleExportData}
        onIntegrations={handleIntegrations}
        onTimeTrackingToggle={handleTimeTrackingToggle}
        onAnalytics={handleAnalytics}
        onShareProject={handleShareProject}
        onArchive={handleArchive}
        notificationCount={notificationCount}
        isTimeTrackingVisible={isTimeTrackingVisible}
      />

      {/* Main Content Area */}
      <div className="pl-16 pr-16 py-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl font-bold text-white mb-2">Project Management Hub</h1>
            <p className="text-white/70">Track progress, manage tasks, and collaborate with your team</p>
          </motion.div>

          {/* Project Overview Cards */}
          <ProjectOverviewCards />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Left Column - Quick Actions */}
            <div className="lg:col-span-1">
              <QuickActionsPanel
                onCreateTask={handleAddTask}
                onInviteCollaborator={handleInviteCollaborator}
                onProjectAnalytics={handleProjectAnalytics}
              />
            </div>

            {/* Center Column - Kanban Board */}
            <div className="lg:col-span-2">
              <Card className="bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-white/10">
                <CardBody className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-4">Task Board</h3>
                  {currentProject && (
                    <KanbanBoard 
                      projectId={currentProject.id}
                      onStartTimer={handleStartTimer}
                      onStopTimer={handleStopTimer}
                      activeTimerTaskId={activeTimerTaskId}
                    />
                  )}
                </CardBody>
              </Card>
            </div>

            {/* Right Column - Time Tracking Panel */}
            <div className="lg:col-span-1">
              <AnimatePresence>
                {isTimeTrackingVisible && (
                  <TimeTrackingPanel
                    activeTask={activeTask}
                    activeTimerTaskId={activeTimerTaskId}
                    onStopTimer={handleStopTimer}
                    isVisible={isTimeTrackingVisible}
                  />
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>

      {/* Task Creation Modal */}
      <TaskCreationModal
        isOpen={isTaskModalOpen}
        onClose={() => setIsTaskModalOpen(false)}
        onTaskCreated={handleTaskCreated}
        projectId={currentProject?.id}
      />
    </div>
  );
};

export default EnhancedTrackPage;
