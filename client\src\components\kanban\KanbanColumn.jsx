import React from 'react';
import { Droppable } from 'react-beautiful-dnd';
import KanbanTask from './KanbanTask';

const KanbanColumn = ({ column, tasks, onEditTask }) => {
  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-4 min-h-[400px] flex flex-col">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center justify-between">
        <span>{column.title}</span>
        <span className="bg-white/10 text-white/70 text-xs px-2 py-1 rounded-full">
          {tasks.length}
        </span>
      </h3>
      <Droppable droppableId={column.id}>
        {(provided, snapshot) => (
          <div
            className={`flex-1 space-y-3 transition-colors duration-200 ${
              snapshot.isDraggingOver ? 'bg-white/5 rounded-lg' : ''
            }`}
            ref={provided.innerRef}
            {...provided.droppableProps}
          >
            {tasks.map((task, index) => (
              <KanbanTask
                key={task.id}
                task={task}
                index={index}
                onEdit={() => onEditTask(task.id)}
              />
            ))}
            {provided.placeholder}
            {tasks.length === 0 && (
              <div className="flex items-center justify-center h-32 text-white/50 text-sm">
                <p>No tasks</p>
              </div>
            )}
          </div>
        )}
      </Droppable>
    </div>
  );
};

export default KanbanColumn;
