-- Fix the remaining 5 database column issues identified in testing
-- These are the final missing columns causing 400 errors

-- ============================================================================
-- ADD MISSING COLUMNS TO TASKS TABLE
-- ============================================================================

-- Add due_date column to tasks (the app queries this)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'tasks' 
        AND column_name = 'due_date'
    ) THEN
        ALTER TABLE public.tasks ADD COLUMN due_date TIMESTAMPTZ;
        RAISE NOTICE 'Added due_date column to tasks table';
    ELSE
        RAISE NOTICE 'due_date column already exists in tasks table';
    END IF;
END $$;

-- ============================================================================
-- ADD MISSING COLUMNS TO PROJECT_CONTRIBUTORS TABLE
-- ============================================================================

-- Add user_id column to project_contributors (the app queries this)
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'project_contributors' 
        AND column_name = 'user_id'
    ) THEN
        ALTER TABLE public.project_contributors ADD COLUMN user_id UUID;
        -- Set existing contributors to have the test user ID
        UPDATE public.project_contributors SET user_id = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b' WHERE user_id IS NULL;
        RAISE NOTICE 'Added user_id column to project_contributors table';
    ELSE
        RAISE NOTICE 'user_id column already exists in project_contributors table';
    END IF;
END $$;

-- ============================================================================
-- CREATE FALLBACK PROJECT IF IT DOESN'T EXIST
-- ============================================================================

-- The app is trying to query a fallback project, let's create it
INSERT INTO public.projects (
    id,
    name,
    description,
    status,
    created_by,
    created_at
) VALUES (
    'fallback-project-ab69ee38-02a0-4744-bbe2-4f6d0bc40e61',
    'Default Project',
    'Fallback project for initial setup',
    'active',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- ADD SAMPLE DATA TO CONTRIBUTION_TRACKING_CONFIG
-- ============================================================================

-- Add a config entry for the fallback project
INSERT INTO public.contribution_tracking_config (
    project_id,
    tracking_enabled,
    point_system,
    reward_structure
) VALUES (
    'fallback-project-ab69ee38-02a0-4744-bbe2-4f6d0bc40e61',
    true,
    '{"base_points": 1, "difficulty_multiplier": {"easy": 1, "medium": 2, "hard": 3}}',
    '{"type": "equal_split"}'
) ON CONFLICT DO NOTHING;

-- ============================================================================
-- ADD SAMPLE TASKS FOR TESTING
-- ============================================================================

-- Add a few sample tasks to make the Track page more interesting
INSERT INTO public.tasks (
    project_id,
    title,
    description,
    status,
    created_by,
    due_date
) VALUES 
(
    'fallback-project-ab69ee38-02a0-4744-bbe2-4f6d0bc40e61',
    'Setup Project Structure',
    'Initialize the basic project structure and documentation',
    'in_progress',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    NOW() + INTERVAL '7 days'
),
(
    'fallback-project-ab69ee38-02a0-4744-bbe2-4f6d0bc40e61',
    'Design User Interface',
    'Create mockups and wireframes for the user interface',
    'todo',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    NOW() + INTERVAL '14 days'
),
(
    'fallback-project-ab69ee38-02a0-4744-bbe2-4f6d0bc40e61',
    'Implement Core Features',
    'Develop the main functionality of the application',
    'todo',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    NOW() + INTERVAL '21 days'
) ON CONFLICT DO NOTHING;

-- ============================================================================
-- ADD PROJECT CONTRIBUTOR ENTRY
-- ============================================================================

-- Add the test user as a contributor to the fallback project
INSERT INTO public.project_contributors (
    project_id,
    user_id,
    role,
    is_admin
) VALUES (
    'fallback-project-ab69ee38-02a0-4744-bbe2-4f6d0bc40e61',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    'owner',
    true
) ON CONFLICT DO NOTHING;

COMMIT;
