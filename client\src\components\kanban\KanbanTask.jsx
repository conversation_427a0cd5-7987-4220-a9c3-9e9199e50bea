import React from 'react';
import { Draggable } from 'react-beautiful-dnd';

const KanbanTask = ({ task, index, onEdit }) => {
  // Get difficulty color based on difficulty level
  const getDifficultyColor = (level) => {
    switch (level) {
      case 'easy':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'medium':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'hard':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'expert':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Format assignee display
  const getAssigneeDisplay = () => {
    if (!task.assignee) return null;

    return (
      <div className="flex items-center gap-2">
        {task.assignee.avatar_url ? (
          <img
            src={task.assignee.avatar_url}
            alt={task.assignee.display_name}
            className="w-6 h-6 rounded-full border border-white/20"
          />
        ) : (
          <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center text-xs text-white/70">
            {task.assignee.display_name?.charAt(0) || '?'}
          </div>
        )}
        <span className="text-xs text-white/70 truncate">{task.assignee.display_name}</span>
      </div>
    );
  };

  return (
    <Draggable draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <div
          className={`
            bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 p-4 cursor-pointer
            transition-all duration-200 hover:bg-white/15 hover:border-white/30
            ${snapshot.isDragging ? 'shadow-lg shadow-black/20 rotate-2 scale-105' : ''}
            ${task.status === 'done' ? 'opacity-75 border-green-500/30' : ''}
          `}
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          onClick={onEdit}
        >
          <div className="flex items-start justify-between mb-3">
            <h4 className="text-white font-medium text-sm leading-tight flex-1 pr-2">{task.title}</h4>
            <div className="text-xs text-white/50 font-mono">#{task.id.substring(0, 8)}</div>
          </div>

          {task.description && (
            <div className="text-white/70 text-xs mb-3 leading-relaxed">
              {task.description.length > 100
                ? `${task.description.substring(0, 100)}...`
                : task.description}
            </div>
          )}

          <div className="space-y-3">
            <div className="flex flex-wrap gap-2">
              {task.task_type && (
                <div className="bg-blue-500/20 text-blue-400 text-xs px-2 py-1 rounded border border-blue-500/30">
                  {task.task_type}
                </div>
              )}

              {task.difficulty_level && (
                <div className={`text-xs px-2 py-1 rounded border ${getDifficultyColor(task.difficulty_level)}`}>
                  {task.difficulty_level}
                </div>
              )}

              {task.logged_hours > 0 && (
                <div className="bg-yellow-500/20 text-yellow-400 text-xs px-2 py-1 rounded border border-yellow-500/30 flex items-center gap-1" title="Logged Hours">
                  <span>⏱</span> {task.logged_hours}h
                </div>
              )}

              {task.status === 'done' && (
                <div className="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded border border-green-500/30 flex items-center gap-1" title="Completed">
                  <span>✓</span> Done
                </div>
              )}
            </div>

            {getAssigneeDisplay()}
          </div>
        </div>
      )}
    </Draggable>
  );
};

export default KanbanTask;
