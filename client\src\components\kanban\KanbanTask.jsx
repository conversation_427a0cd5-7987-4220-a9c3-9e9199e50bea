import React from 'react';
import { Draggable } from 'react-beautiful-dnd';

const KanbanTask = ({ task, index, onEdit }) => {
  // Get difficulty color based on difficulty level
  const getDifficultyColor = (level) => {
    switch (level) {
      case 'easy':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'medium':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'hard':
        return 'bg-red-500/20 text-red-400 border-red-500/30';
      case 'expert':
        return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Format assignee display
  const getAssigneeDisplay = () => {
    if (!task.assignee) return null;

    return (
      <div className="flex items-center gap-2 bg-white/5 rounded-md px-2 py-1 border border-white/10">
        {task.assignee.avatar_url ? (
          <img
            src={task.assignee.avatar_url}
            alt={task.assignee.display_name}
            className="w-5 h-5 rounded-full border border-white/30"
          />
        ) : (
          <div className="w-5 h-5 rounded-full bg-gradient-to-br from-blue-400/30 to-purple-400/30 border border-white/20 flex items-center justify-center text-xs text-white/80 font-medium">
            {task.assignee.display_name?.charAt(0) || '?'}
          </div>
        )}
        <span className="text-xs text-white/80 truncate font-medium">{task.assignee.display_name}</span>
      </div>
    );
  };

  return (
    <Draggable draggableId={task.id} index={index}>
      {(provided, snapshot) => (
        <div
          className={`
            bg-gray-800/90 backdrop-blur-md rounded-lg border border-white/30 p-4 cursor-pointer
            shadow-lg shadow-black/10 transition-all duration-200
            hover:bg-gray-700/90 hover:border-white/50 hover:shadow-xl hover:shadow-black/20
            ${snapshot.isDragging ? 'shadow-2xl shadow-black/40 rotate-2 scale-105 border-blue-400/50 bg-gray-700/95' : ''}
            ${task.status === 'done' ? 'opacity-80 border-green-400/40 bg-green-900/20' : ''}
            relative overflow-hidden
          `}
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          onClick={onEdit}
        >
          {/* Subtle gradient overlay for depth */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none rounded-lg"></div>

          <div className="relative z-10">
            <div className="flex items-start justify-between mb-3">
              <h4 className="text-white font-semibold text-sm leading-tight flex-1 pr-2">{task.title}</h4>
              <div className="text-xs text-white/60 font-mono bg-white/10 px-2 py-1 rounded">#{task.id.substring(0, 8)}</div>
            </div>

            {task.description && (
              <div className="text-white/80 text-xs mb-3 leading-relaxed">
                {task.description.length > 100
                  ? `${task.description.substring(0, 100)}...`
                  : task.description}
              </div>
            )}

            <div className="space-y-3">
              <div className="flex flex-wrap gap-2">
                {task.task_type && (
                  <div className="bg-blue-500/30 text-blue-300 text-xs px-2 py-1 rounded-md border border-blue-400/40 font-medium">
                    {task.task_type}
                  </div>
                )}

                {task.difficulty_level && (
                  <div className={`text-xs px-2 py-1 rounded-md border font-medium ${getDifficultyColor(task.difficulty_level)}`}>
                    {task.difficulty_level}
                  </div>
                )}

                {task.logged_hours > 0 && (
                  <div className="bg-yellow-500/30 text-yellow-300 text-xs px-2 py-1 rounded-md border border-yellow-400/40 flex items-center gap-1 font-medium" title="Logged Hours">
                    <span>⏱</span> {task.logged_hours}h
                  </div>
                )}

                {task.status === 'done' && (
                  <div className="bg-green-500/30 text-green-300 text-xs px-2 py-1 rounded-md border border-green-400/40 flex items-center gap-1 font-medium" title="Completed">
                    <span>✓</span> Done
                  </div>
                )}
              </div>

              {getAssigneeDisplay()}
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );
};

export default KanbanTask;
