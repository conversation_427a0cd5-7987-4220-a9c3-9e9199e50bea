import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>, But<PERSON> } from '@heroui/react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Plus, 
  UserPlus, 
  Target, 
  BarChart3
} from 'lucide-react';

/**
 * Quick Actions Panel Component
 * 
 * 4-button grid for common actions:
 * - Create Task
 * - Invite Collaborator
 * - Check Mission Board (links to Earn section)
 * - Project Analytics
 */
const QuickActionsPanel = ({ 
  onCreateTask, 
  onInviteCollaborator, 
  onProjectAnalytics,
  className = "" 
}) => {
  const navigate = useNavigate();

  const handleMissionBoard = () => {
    navigate('/earn');
  };

  const actions = [
    {
      id: 'create-task',
      title: 'Create Task',
      description: 'Add a new task to your project',
      icon: Plus,
      onClick: onCreateTask,
      color: 'success',
      bgGradient: 'from-green-500/20 to-emerald-500/20',
      hoverGradient: 'hover:from-green-500/30 hover:to-emerald-500/30'
    },
    {
      id: 'invite-collaborator',
      title: 'Invite Collaborator',
      description: 'Add team members to your project',
      icon: UserPlus,
      onClick: onInviteCollaborator,
      color: 'primary',
      bgGradient: 'from-blue-500/20 to-cyan-500/20',
      hoverGradient: 'hover:from-blue-500/30 hover:to-cyan-500/30'
    },
    {
      id: 'mission-board',
      title: 'Check Mission Board',
      description: 'Browse available missions and bounties',
      icon: Target,
      onClick: handleMissionBoard,
      color: 'warning',
      bgGradient: 'from-yellow-500/20 to-amber-500/20',
      hoverGradient: 'hover:from-yellow-500/30 hover:to-amber-500/30 hover:shadow-lg hover:shadow-yellow-500/20',
      special: true, // This is the mission board link
      badge: 'New Missions Available!'
    },
    {
      id: 'project-analytics',
      title: 'Project Analytics',
      description: 'View detailed project insights',
      icon: BarChart3,
      onClick: onProjectAnalytics,
      color: 'secondary',
      bgGradient: 'from-purple-500/20 to-pink-500/20',
      hoverGradient: 'hover:from-purple-500/30 hover:to-pink-500/30'
    }
  ];

  return (
    <Card className={`bg-gradient-to-br from-gray-900/50 to-gray-800/50 border border-white/10 ${className}`}>
      <CardBody className="p-6">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-white">Quick Actions</h3>
          <p className="text-sm text-white/60">Common project management tasks</p>
        </div>

        <div className="grid grid-cols-2 gap-4">
          {actions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                onClick={action.onClick}
                className={`
                  w-full h-auto p-4 flex flex-col items-center gap-3
                  bg-gradient-to-br ${action.bgGradient}
                  ${action.hoverGradient}
                  border ${action.special ? 'border-yellow-500/50' : 'border-white/10'}
                  ${action.special ? 'hover:border-yellow-400/70' : 'hover:border-white/20'}
                  transition-all duration-200
                  ${action.special ? 'ring-2 ring-yellow-500/30 shadow-lg shadow-yellow-500/10' : ''}
                `}
                variant="light"
              >
                <div className={`
                  p-3 rounded-full 
                  ${action.special 
                    ? 'bg-yellow-500/20 text-yellow-400' 
                    : `bg-${action.color}-500/20 text-${action.color}-400`
                  }
                `}>
                  <action.icon className="w-6 h-6" />
                </div>
                
                <div className="text-center">
                  <div className={`
                    font-medium text-sm
                    ${action.special ? 'text-yellow-200' : 'text-white'}
                  `}>
                    {action.title}
                  </div>
                  <div className="text-xs text-white/60 mt-1">
                    {action.description}
                  </div>
                </div>

                {action.special && (
                  <div className="absolute -top-1 -right-1">
                    <div className="bg-yellow-500 text-black text-xs px-2 py-1 rounded-full font-semibold animate-pulse">
                      NEW
                    </div>
                  </div>
                )}
              </Button>
            </motion.div>
          ))}
        </div>

        <div className="mt-4 pt-4 border-t border-white/10">
          <p className="text-xs text-white/50 text-center">
            💡 Tip: Use the Mission Board to find additional revenue opportunities
          </p>
        </div>
      </CardBody>
    </Card>
  );
};

export default QuickActionsPanel;
