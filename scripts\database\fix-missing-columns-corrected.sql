-- Corrected fix for missing database columns - no fallbacks, just proper database structure
-- This version checks what columns actually exist before trying to insert

-- ============================================================================
-- ADD MISSING COLUMNS TO TASKS TABLE
-- ============================================================================

-- Add due_date column to tasks table
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'tasks' 
        AND column_name = 'due_date'
    ) THEN
        ALTER TABLE public.tasks ADD COLUMN due_date TIMESTAMPTZ;
        RAISE NOTICE 'Added due_date column to tasks table';
    ELSE
        RAISE NOTICE 'due_date column already exists in tasks table';
    END IF;
END $$;

-- ============================================================================
-- ADD MISSING COLUMNS TO PROJECT_CONTRIBUTORS TABLE
-- ============================================================================

-- Add user_id column to project_contributors table
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'project_contributors' 
        AND column_name = 'user_id'
    ) THEN
        ALTER TABLE public.project_contributors ADD COLUMN user_id UUID REFERENCES auth.users(id);
        RAISE NOTICE 'Added user_id column to project_contributors table';
    ELSE
        RAISE NOTICE 'user_id column already exists in project_contributors table';
    END IF;
END $$;

-- ============================================================================
-- HANDLE CONTRIBUTION_TRACKING_CONFIG TABLE
-- ============================================================================

-- First, check if the table exists and what columns it has
DO $$
DECLARE
    table_exists BOOLEAN;
    has_tracking_enabled BOOLEAN;
    has_point_system BOOLEAN;
    has_reward_structure BOOLEAN;
BEGIN
    -- Check if table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'contribution_tracking_config'
    ) INTO table_exists;

    IF table_exists THEN
        -- Check for specific columns
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'contribution_tracking_config' 
            AND column_name = 'tracking_enabled'
        ) INTO has_tracking_enabled;

        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'contribution_tracking_config' 
            AND column_name = 'point_system'
        ) INTO has_point_system;

        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = 'contribution_tracking_config' 
            AND column_name = 'reward_structure'
        ) INTO has_reward_structure;

        -- Add missing columns
        IF NOT has_tracking_enabled THEN
            ALTER TABLE public.contribution_tracking_config ADD COLUMN tracking_enabled BOOLEAN DEFAULT true;
            RAISE NOTICE 'Added tracking_enabled column to contribution_tracking_config';
        END IF;

        IF NOT has_point_system THEN
            ALTER TABLE public.contribution_tracking_config ADD COLUMN point_system JSONB DEFAULT '{"base_points": 1}';
            RAISE NOTICE 'Added point_system column to contribution_tracking_config';
        END IF;

        IF NOT has_reward_structure THEN
            ALTER TABLE public.contribution_tracking_config ADD COLUMN reward_structure JSONB DEFAULT '{"type": "equal_split"}';
            RAISE NOTICE 'Added reward_structure column to contribution_tracking_config';
        END IF;

        RAISE NOTICE 'contribution_tracking_config table updated with missing columns';
    ELSE
        -- Create the table from scratch
        CREATE TABLE public.contribution_tracking_config (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
            tracking_enabled BOOLEAN DEFAULT true,
            point_system JSONB DEFAULT '{"base_points": 1}',
            reward_structure JSONB DEFAULT '{"type": "equal_split"}',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            
            UNIQUE(project_id)
        );

        -- Add RLS policy
        CREATE POLICY "Users can manage their project configs" ON public.contribution_tracking_config
            FOR ALL USING (
                project_id IN (
                    SELECT id FROM public.projects 
                    WHERE created_by = auth.uid()
                )
            );

        -- Enable RLS
        ALTER TABLE public.contribution_tracking_config ENABLE ROW LEVEL SECURITY;

        RAISE NOTICE 'Created contribution_tracking_config table from scratch';
    END IF;
END $$;

-- ============================================================================
-- CREATE A REAL PROJECT FOR THE TEST USER (NOT A FALLBACK)
-- ============================================================================

-- Create a real project for the test user if they don't have one
INSERT INTO public.projects (
    name,
    description,
    status,
    created_by,
    project_type
) 
SELECT 
    'My First Project',
    'Getting started with project management',
    'active',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    'software'
WHERE NOT EXISTS (
    SELECT 1 FROM public.projects 
    WHERE created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
    AND status IN ('active', 'planning')
);

-- ============================================================================
-- ADD SAMPLE TASKS FOR THE REAL PROJECT
-- ============================================================================

-- Add some sample tasks to the user's project
INSERT INTO public.tasks (
    project_id,
    title,
    description,
    status,
    created_by,
    due_date
)
SELECT 
    p.id,
    'Setup Development Environment',
    'Configure development tools and workspace',
    'todo',
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    NOW() + INTERVAL '7 days'
FROM public.projects p
WHERE p.created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
AND p.status = 'active'
AND NOT EXISTS (
    SELECT 1 FROM public.tasks t 
    WHERE t.project_id = p.id 
    AND t.title = 'Setup Development Environment'
)
LIMIT 1;

-- Add project contributor entry
INSERT INTO public.project_contributors (
    project_id,
    user_id,
    role,
    is_admin
)
SELECT 
    p.id,
    '93cbbbed-2772-4922-b7d7-d07fdc1aa62b',
    'owner',
    true
FROM public.projects p
WHERE p.created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
AND p.status = 'active'
AND NOT EXISTS (
    SELECT 1 FROM public.project_contributors pc 
    WHERE pc.project_id = p.id 
    AND pc.user_id = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
)
LIMIT 1;

-- Add contribution tracking config for the project (using dynamic column insertion)
DO $$
DECLARE
    project_record RECORD;
    has_tracking_enabled BOOLEAN;
    has_point_system BOOLEAN;
    has_reward_structure BOOLEAN;
BEGIN
    -- Check what columns exist in contribution_tracking_config
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'contribution_tracking_config' 
        AND column_name = 'tracking_enabled'
    ) INTO has_tracking_enabled;

    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'contribution_tracking_config' 
        AND column_name = 'point_system'
    ) INTO has_point_system;

    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'contribution_tracking_config' 
        AND column_name = 'reward_structure'
    ) INTO has_reward_structure;

    -- Get the user's project
    SELECT id INTO project_record FROM public.projects 
    WHERE created_by = '93cbbbed-2772-4922-b7d7-d07fdc1aa62b'
    AND status = 'active'
    LIMIT 1;

    IF project_record.id IS NOT NULL THEN
        -- Check if config already exists for this project
        IF NOT EXISTS (
            SELECT 1 FROM public.contribution_tracking_config
            WHERE project_id = project_record.id
        ) THEN
            -- Insert config based on available columns
            IF has_tracking_enabled AND has_point_system AND has_reward_structure THEN
                INSERT INTO public.contribution_tracking_config (
                    project_id,
                    tracking_enabled,
                    point_system,
                    reward_structure
                ) VALUES (
                    project_record.id,
                    true,
                    '{"base_points": 1, "difficulty_multiplier": {"easy": 1, "medium": 2, "hard": 3}}',
                    '{"type": "equal_split"}'
                );
            ELSE
                -- Insert with just project_id if other columns don't exist
                INSERT INTO public.contribution_tracking_config (project_id)
                VALUES (project_record.id);
            END IF;
        END IF;
        
        RAISE NOTICE 'Added contribution tracking config for project %', project_record.id;
    END IF;
END $$;

COMMIT;
